'use client';

import { profile } from 'console';
import { useState } from 'react';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import Link from 'next/link';
import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { faBriefcase, faListCheck, faShareNodes } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ConnectMessageForm } from '@/components/network/ConnectMessageForm';
import type { Sponsor } from '@/components/opportunities/OpportunitiesSponsoredCarousel';
// import { OpportunitiesSponsoredCarousel } from '@/components/opportunities/OpportunitiesSponsoredCarousel';
import OpportunityCard from '@/components/opportunities/OpportunityCard';
import Avatar from '@/components/shared/Avatar';
import AthleteCard, { CareerInterest } from '@/components/shared/cards/AthleteCard';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
// import ModuleCarousel from '@/components/shared/interactive/ModuleCarousel';
// import NotificationCarousel from '@/components/shared/NotificationCarousel';
import { useAuth } from '@/hooks/useAuth';
import { useConnectionPermissions } from '@/hooks/useConnectionPermissions';
import { useDashboard } from '@/hooks/useDashboard';
import { useSystemAlerts } from '@/hooks/useSystemAlerts';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import type { UserBasicData } from '@/services/networking.service';
import { useModalStore } from '@/stores/modal.store';

const LazyNotificationCarousel = dynamic(() => import('@/components/shared/NotificationCarousel'), {
  ssr: false,
});

const LazyOpportunitiesSponsoredCarousel = dynamic(
  () =>
    import('@/components/opportunities/OpportunitiesSponsoredCarousel').then(
      mod => mod.OpportunitiesSponsoredCarousel
    ),
  {
    ssr: false,
  }
);

const LazyModuleCarousel = dynamic(() => import('@/components/shared/interactive/ModuleCarousel'), {
  ssr: false,
});

// Define types for the interest and sport objects in the API response
interface InterestObject {
  id: number;
  name: string;
  icon?: string;
}

interface SportObject {
  id: number;
  name: string;
}



export const fpoRecommendedXFactorModules = [
  {
    id: '1',
    title: 'Mental Toughness',
    subtitle: 'With Doug Baldwin',
    coverImage: '/mock/mock-cover-1.jpg',
    href: '/x-factor/module/1',
  },
  {
    id: '2',
    title: 'Leadership',
    subtitle: 'With Jack Sock',
    coverImage: '/mock/mock-cover-2.jpg',
    href: '/x-factor/module/2',
  },
  {
    id: '3',
    title: 'Team Building',
    subtitle: 'With Jason Collins',
    coverImage: '/mock/mock-cover-3.jpg',
    href: '/x-factor/module/3',
  },
  {
    id: '4',
    title: 'Goal Setting',
    subtitle: 'With Jenna Dohms',
    coverImage: '/mock/mock-cover-1.jpg',
    href: '/x-factor/module/4',
  },
  {
    id: '5',
    title: 'Performance',
    subtitle: 'With John Smith',
    coverImage: '/mock/mock-cover-2.jpg',
    href: '/x-factor/module/5',
  },
];

export const fpoSponsoredOpportunities: Sponsor[] = [
  {
    id: '1',
    label: 'SPONSORED',
    logoUrl: '/mock/TheHomeDepot.png',
    coverImageUrl: '/images/profile-banner.png',
    tagline:
      'Christ-Centered Tutorial-Based Education That Fosters Intellectual, Spiritual, And Vocational Formation',
    ctaText: 'Learn More',
    ctaUrl: 'https://example.com/christian-halls',
  },
  {
    id: '4',
    label: 'SPONSORED',
    logoUrl: '/mock/TheHomeDepot.png',
    coverImageUrl: '/images/profile-banner.png',
    tagline:
      'Christ-Centered Tutorial-Based Education That Fosters Intellectual, Spiritual, And Vocational Formation',
    ctaText: 'Learn More',
    ctaUrl: 'https://example.com/christian-halls',
  },
];

export const fpoRecommendedConnections = [
  {
    first_name: 'Athlete',
    last_name: '2-5',
    high_school: 'School 2',
    career_interests: [
      'Coaching',
      'Finance',
      'Human Services',
      'Installation, Repair & Maintenance',
    ],
    sports: ['Badminton'],
    state: 'New York',
    id: 18,
    profile_type: 'positive_athlete',
    graduation_year: 2026,
    state_code: 'NY',
    profile_image_url: '',
    class_of: 'CLASS OF 2026',
  },
  {
    first_name: 'Coach',
    last_name: 'Number 2',
    high_school: 'School 2',
    career_interests: ['Architecture', 'Energy', 'Marketing', 'Social Media Management'],
    sports: ['Cheerleading', 'Ultimate Frisbee'],
    state: 'New York',
    id: 10,
    profile_type: 'positive_coach',
    graduation_year: null,
    state_code: 'NY',
    profile_image_url: '',
    class_of: null,
  },
  {
    first_name: 'Athlete',
    last_name: '1-3',
    high_school: 'School 1',
    career_interests: [
      'Business Management & Administration',
      'Health & Medical',
      'Social Media Management',
    ],
    sports: ['Rugby', 'Archery'],
    state: 'Minnesota',
    id: 6,
    profile_type: 'positive_athlete',
    graduation_year: 2025,
    state_code: 'MN',
    profile_image_url: '',
    class_of: 'CLASS OF 2025',
  },
  {
    first_name: 'Professional',
    last_name: 'User 2',
    high_school: null,
    career_interests: [],
    sports: [],
    state: 'Wisconsin',
    id: 20,
    profile_type: 'professional',
    graduation_year: null,
    state_code: 'WI',
    profile_image_url: '',
    class_of: null,
  },
];

export const fpoRecommendedOpportunities = [
  {
    id: 43,
    title: 'Apprenticeship: Data Analytics',
    description:
      'Distinctio quidem provident dolorum ex quia modi dolorum accusamus. Aut nulla officiis ipsa excepturi...',
    details:
      'Join our Data Analytics Apprenticeship program and gain hands-on experience with real-world data. This program includes:<br/><br/>• Comprehensive training in data analysis tools and techniques<br/>• Mentorship from industry professionals<br/>• Practical experience with business analytics<br/>• Opportunity for full-time employment upon completion',
    location: 'San Diego, CA',
    state_code: 'CA',
    location_type: 'onsite',
    subtype: 'degree_program',
    organization_name: "O'Keefe Group",
    organization_logo: 'http://localhost:8000/storage/321/conversions/home-depot-thumbnail.jpg',
    industries: ['Retail', 'Education', 'Hospitality'],
    term: '6_months',
  },
  {
    id: 72,
    title: 'Training Program: Engineering',
    description: 'Nam qui ipsa consequatur itaque dolore. Ab repudiandae velit totam at vel...',
    details:
      'Our Engineering Training Program offers a unique blend of theoretical knowledge and practical application:<br/><br/>• Full-stack engineering fundamentals<br/>• Cloud computing and DevOps practices<br/>• Agile development methodologies<br/>• Industry-recognized certifications<br/>• Project-based learning experiences',
    location: 'Tucson, AZ',
    state_code: 'AZ',
    location_type: 'remote',
    subtype: 'continuing_education',
    organization_name: 'Klocko-Jenkins',
    organization_logo: 'http://localhost:8000/storage/329/conversions/delta-thumbnail.jpg',
    industries: ['Construction', 'Finance & Banking', 'Transportation & Logistics'],
    term: '12_months',
  },
  {
    id: 21,
    title: 'Training Program: Project Management',
    description: 'Consequuntur maiores labore saepe accusamus aperiam non beatae ut...',
    details:
      'Master the art of project management with our comprehensive training program:<br/><br/>• PMI-aligned curriculum<br/>• Real-world project simulations<br/>• Risk management strategies<br/>• Team leadership development<br/>• Scrum and Agile methodologies<br/>• Preparation for PMP certification',
    location: 'San Francisco, CA',
    state_code: 'CA',
    location_type: 'remote',
    subtype: 'internship',
    organization_name: 'Marquardt-Ratke',
    organization_logo: 'http://localhost:8000/storage/327/conversions/delta-thumbnail.jpg',
    industries: ['Hospitality', 'Transportation & Logistics', 'Media & Entertainment'],
    term: '3_months',
  },
  {
    id: 115,
    title: 'Educational Program: Digital Marketing',
    description:
      'Non ipsum veritatis praesentium eos odio nisi. Aperiam quasi sit dignissimos occaecati...',
    details:
      'Dive into the world of digital marketing with our comprehensive educational program:<br/><br/>• Social media marketing strategies<br/>• SEO and content optimization<br/>• Email marketing campaigns<br/>• Analytics and data interpretation<br/>• Marketing automation tools<br/>• Portfolio development opportunities',
    location: 'Las Vegas, NV',
    state_code: 'NV',
    location_type: 'hybrid',
    subtype: 'certificate_program',
    organization_name: 'Sipes, Hermiston and Flatley',
    organization_logo: 'http://localhost:8000/storage/325/conversions/delta-thumbnail.jpg',
    industries: ['Education'],
    term: '4_months',
  },
];

export default function DashboardPage() {
  const [selectedThreadId, setSelectedThreadId] = useState<string | null>(null);
  const { open } = useModalStore();
  const checkCanConnect = useConnectionPermissions();
  const { avatarUrl } = usePositiveAthleteProfile();
  const { user, profileType } = useAuth();

  // Fetch real dashboard data using our new hook
  const {
    recommendedModules,
    isLoadingRecommendedModules,
    recommendedAthletesError,
    recommendedModulesError,

    recommendedAthletes,
    isLoadingRecommendedAthletes,

    recommendedOpportunities,
    isLoadingRecommendedOpportunities,
    recommendedOpportunitiesError,
  } = useDashboard({
    athletesLimit: 4, // Match the current number of displayed athletes
    modulesLimit: 10, // Increased from 5 to 10 to show more modules
    opportunitiesLimit: 4, // Set limit for opportunities
  });

  // Fetch system alerts using our new hook
  const {
    notifications: systemAlertNotifications,
    isLoadingAlerts,
    handleAlertView,
    handleAlertClick,
  } = useSystemAlerts();

  // Only show notifications if we have system alerts from the backend
  const shouldShowNotifications = !isLoadingAlerts && systemAlertNotifications.length > 0;

  // Transform recommendedModules into the format expected by ModuleCarousel
  const formattedModules = recommendedModules.map(module => ({
    id: module.id.toString(),
    title: module.title,
    subtitle: module.category,
    coverImage: module.thumbnail,
    href: `/x-factor/modules/${module.id}`,
  }));

  const handleConnect = (user: (typeof fpoRecommendedConnections)[0]) => {
    // Check if the current user can connect with the target user
    const canConnect = checkCanConnect(user.profile_type);

    if (!canConnect) {
      return;
    }

    const recipient: UserBasicData = {
      id: user.id,
      firstName: user.first_name,
      lastName: user.last_name,
      profileImageUrl: user.profile_image_url,
    };

    open(<ConnectMessageForm recipient={recipient} />, 'md');
  };

  return (
    <div className="pa-container py-20 space-y-10">
      {/* Welcome Section */}
      <div className="flex items-center gap-4">
        <Avatar
          src={avatarUrl || undefined}
          firstName={user?.first_name}
          lastName={user?.last_name}
          size="xl"
        />
        <div className="space-y-2">
          <span className="pa-eyebrow text-brand-red">WELCOME!</span>

          <h1 className="text-3xl font-bold text-text-primary">
            {user?.first_name} {user?.last_name}
          </h1>

          <Link
            href="/profile"
            className="flex items-center gap-2 text-text-blue text-sm font-normal"
          >
            Go To Profile
            <FontAwesomeIcon
              icon={faArrowRight}
              className="text-sm text-current"
              aria-hidden="true"
            />
          </Link>
        </div>
      </div>

      {/* Notification Carousel - Only show if there are system alerts */}
      {isLoadingAlerts ? (
        <div className="py-8 text-center">Loading alerts...</div>
      ) : shouldShowNotifications ? (
        <LazyNotificationCarousel
          notifications={systemAlertNotifications}
          onNotificationView={handleAlertView}
          onNotificationClick={handleAlertClick}
        />
      ) : null}

      {/* Featured Modules Carousel */}
      <Card>
        {isLoadingRecommendedModules ? (
          <div className="py-8 text-center">Loading recommended modules...</div>
        ) : recommendedModulesError ? (
          <div className="py-8 text-center text-red-500">Error loading recommended modules</div>
        ) : (
          <LazyModuleCarousel
            overline="Recommended X FACTOR MODULES"
            titleIcon={faListCheck}
            modules={formattedModules}
            navigationContent={
              <Link
                href="/x-factor?tab=browse"
                className="pl-4 flex items-center gap-2 text-sm text-brand-red"
              >
                Browse
                <FontAwesomeIcon icon={faArrowRight} className="text-sm" aria-hidden="true" />
              </Link>
            }
            className="mb-8"
          />
        )}
      </Card>

      {/* Sponsored Opportunities Carousel */}
      <LazyOpportunitiesSponsoredCarousel sponsors={fpoSponsoredOpportunities} />

      {/* Recommended Connections */}
      <Card>
        <CardHeader
          title="Recommended Connections"
          titleIcon={faShareNodes}
          customEdit={
            <Link href="/network" className="flex items-center gap-2 text-sm text-brand-red">
              Network
              <FontAwesomeIcon icon={faArrowRight} className="text-sm" aria-hidden="true" />
            </Link>
          }
          className="mb-8"
        />

        <div className="block space-y-4">
          {isLoadingRecommendedAthletes ? (
            <div className="py-4 text-center">Loading recommended connections...</div>
          ) : recommendedAthletesError ? (
            <div className="py-4 text-center text-red-500">
              Error loading recommended connections
            </div>
          ) : recommendedAthletes.length === 0 ? (
            <div className="py-4 text-center">No recommended connections available</div>
          ) : (
            recommendedAthletes.map(athlete => {
              // Extract names from interests and sports objects
              const interestNames = Array.isArray(athlete.interests)
                ? athlete.interests.map(interest =>
                    typeof interest === 'string' ? interest : (interest as InterestObject).name
                  )
                : [];

              const sportNames = Array.isArray(athlete.sports)
                ? athlete.sports.map(sport =>
                    typeof sport === 'string' ? sport : (sport as SportObject).name
                  )
                : [];

              const connection = {
                id: athlete.id,
                first_name: athlete.first_name,
                last_name: athlete.last_name,
                profile_type: profileType,
                profile_image_url: '',
                state: '',
                high_school: '',
                graduation_year: null,
                career_interests: interestNames,
              };

              const careerInterests: CareerInterest[] = interestNames.map(interest => ({
                name: interest,
                count: 1,
              }));

              return (
                <AthleteCard
                  key={athlete.id}
                  id={athlete.id.toString()}
                  name={athlete.name}
                  graduationYear={''}
                  location={''}
                  highSchool={athlete.school}
                  avatar={athlete?.avatar || ''}
                  careerInterests={careerInterests}
                  onConnect={() => handleConnect(connection as any)}
                  canConnect={checkCanConnect(profileType)}
                />
              );
            })
          )}
        </div>
      </Card>

      {/* Recommended Opportunities */}
      <Card>
        <CardHeader
          title="Recommended Opportunities"
          titleIcon={faBriefcase}
          customEdit={
            <Link href="/opportunities" className="flex items-center gap-2 text-sm text-brand-red">
              Opportunities
              <FontAwesomeIcon icon={faArrowRight} className="text-sm" aria-hidden="true" />
            </Link>
          }
          className="mb-8"
        />

        <div className="block space-y-4">
          {isLoadingRecommendedOpportunities ? (
            <div className="py-4 text-center">Loading recommended opportunities...</div>
          ) : recommendedOpportunitiesError ? (
            <div className="py-4 text-center text-red-500">
              Error loading recommended opportunities
            </div>
          ) : recommendedOpportunities.length === 0 ? (
            <div className="py-4 text-center">No recommended opportunities available</div>
          ) : (
            recommendedOpportunities.map(opportunity => (
              <OpportunityCard key={opportunity.id} opportunity={opportunity} />
            ))
          )}
        </div>
      </Card>
    </div>
  );
}
