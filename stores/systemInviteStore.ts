import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { SystemInviteResponse } from '@/services/systemInvite.service';

interface SystemInviteStore {
  inviteData: SystemInviteResponse | null;
  setInviteData: (data: SystemInviteResponse) => void;
  clearInviteData: () => void;
}

const useSystemInviteStore = create<SystemInviteStore>()(
  persist(
    set => ({
      inviteData: null,
      setInviteData: data => set({ inviteData: data }),
      clearInviteData: () => set({ inviteData: null }),
    }),
    {
      name: 'positive-athlete-invite-storage',
    }
  )
);

// Helper to check hydration status
export const hasHydrated = () => useSystemInviteStore.persist.hasHydrated();

export { useSystemInviteStore };
