import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import type { Interest } from '@/services/interest.service';

// Payload interfaces based on backend DTOs
export interface AlumniLifeStageData {
  life_stage: string; // e.g., 'college_student', 'college_graduate', 'professional'
  intended_profile_type: string; // e.g., 'college_athlete', 'professional'
}

export interface AlumniAccountInfoData {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  street_address?: string;
  unit?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  password?: string;
  password_confirmation?: string;
}

export interface AlumniCollegeDetailsData {
  college?: string;
  state?: string; // Added state for college location
  graduation_year?: string;
  gpa?: string;
  gender?: string;
  height?: string;
  weight?: string;
  career_interests?: Interest[];
  twitter?: string;
  instagram?: string;
  facebook?: string;
  hudl?: string;
  custom_link?: string;
  profile_photo_url?: string | null; // Changed from profile_photo: File | string | null
}

export interface AlumniProfessionalDetailsData {
  employer?: string;
  job_title?: string; // Added for completeness, often part of professional details
  state?: string; // State for professional context (e.g., work location)
  interests?: Interest[];
  twitter?: string;
  instagram?: string;
  facebook?: string;
  hudl?: string;
  custom_link?: string;
  profile_photo_url?: string | null; // Changed from profile_photo: File | string | null
}

interface AlumniOnboardingState {
  currentStep: string;
  lifeStageSelection: Partial<AlumniLifeStageData>;
  accountInfo: Partial<AlumniAccountInfoData>;
  collegeDetails: Partial<AlumniCollegeDetailsData>;
  professionalDetails: Partial<AlumniProfessionalDetailsData>;
  errors: Record<string, string[]> | null;
  setCurrentStep: (step: string) => void;
  setLifeStageSelection: (data: Partial<AlumniLifeStageData>) => void;
  setAccountInfo: (data: Partial<AlumniAccountInfoData>) => void;
  setCollegeDetails: (data: Partial<AlumniCollegeDetailsData>) => void;
  setProfessionalDetails: (data: Partial<AlumniProfessionalDetailsData>) => void;
  setErrors: (errors: { message: string; errors: Record<string, string[]> } | null) => void;
  reset: () => void;
}

const initialState = {
  currentStep: 'life_stage_selection', // Default starting step
  lifeStageSelection: {},
  accountInfo: {},
  collegeDetails: { profile_photo_url: null }, // Initialize new field
  professionalDetails: { profile_photo_url: null }, // Initialize new field
  errors: null,
};

export const useAlumniOnboardingStore = create<AlumniOnboardingState>()(
  persist(
    (set, get) => ({
      ...initialState,
      setCurrentStep: currentStep => set({ currentStep, errors: null }), // Clear errors on step change
      setLifeStageSelection: data =>
        set(state => ({
          lifeStageSelection: { ...state.lifeStageSelection, ...data },
          errors: null,
        })),
      setAccountInfo: data =>
        set(state => ({
          accountInfo: { ...state.accountInfo, ...data },
          errors: null,
        })),
      setCollegeDetails: data =>
        set(state => ({
          collegeDetails: { ...state.collegeDetails, ...data },
          errors: null,
        })),
      setProfessionalDetails: data =>
        set(state => ({
          professionalDetails: { ...state.professionalDetails, ...data },
          errors: null,
        })),
      setErrors: errorPayload => set({ errors: errorPayload ? errorPayload.errors : null }),
      reset: () => set(initialState),
    }),
    {
      name: 'alumni-onboarding-store',
      storage: createJSONStorage(() => localStorage),
      // onRehydrateStorage: () => state => {
      //   // Optional: You can do something when the store is rehydrated
      //   if (state) {
      //     state.currentStep = state.currentStep || initialState.currentStep;
      //   }
      // },
    }
  )
);

// Helper to check if the store has been hydrated
export const hasHydratedAlumni = () => useAlumniOnboardingStore.persist.hasHydrated();
