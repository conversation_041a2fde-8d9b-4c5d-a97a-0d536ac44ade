import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AuthResponse, User } from '@/types/auth';

export type ProfileType =
  | 'positive_athlete'
  | 'positive_coach'
  | 'athlete'
  | 'coach'
  | 'parent'
  | 'sponsor'
  | 'utility'
  | 'admin'
  | 'college_athlete'
  | 'professional'
  | 'team_student'
  | 'team_coach'
  | 'athletics_director'
  | 'alumni'
  | null;

// Enum-like object for ProfileType values
export const ProfileTypes = {
  POSITIVE_ATHLETE: 'positive_athlete' as const,
  POSITIVE_COACH: 'positive_coach' as const,
  ATHLETE: 'athlete' as const,
  COACH: 'coach' as const,
  PARENT: 'parent' as const,
  SPONSOR: 'sponsor' as const,
  UTILITY: 'utility' as const,
  ADMIN: 'admin' as const,
  COLLEGE_ATHLETE: 'college_athlete' as const,
  PROFESSIONAL: 'professional' as const,
  TEAM_STUDENT: 'team_student' as const,
  TEAM_COACH: 'team_coach' as const,
  ATHLETICS_DIRECTOR: 'athletics_director' as const,
  ALUMNI: 'alumni' as const, // Alumni is a new profile type for the alumni onboarding flow
} as const;

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  profileType: ProfileType;
  login: (params: { user: User }) => void;
  logout: () => void;
  // Helper methods for checking profile types
  isPositiveAthlete: () => boolean;
  isPositiveCoach: () => boolean;
  isAthlete: () => boolean;
  isCoach: () => boolean;
  isParent: () => boolean;
  isSponsor: () => boolean;
  isAdmin: () => boolean;
  isAthleticsDirector: () => boolean;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      profileType: null,
      login: ({ user }) => {
        // Only update if the state actually changes
        const current = get();
        if (current.user?.id !== user.id || !current.isAuthenticated) {
          set({
            user,
            isAuthenticated: true,
            profileType: user.profile_type as ProfileType,
          });
        }
      },
      logout: () => {
        // Only update if we're actually logged in
        const current = get();
        if (current.isAuthenticated || current.user) {
          set({
            user: null,
            isAuthenticated: false,
            profileType: null,
          });
        }
      },
      // Helper methods implementation
      isPositiveAthlete: () => get().profileType === 'positive_athlete',
      isPositiveCoach: () => get().profileType === 'positive_coach',
      isAthlete: () => {
        const type = get().profileType;
        return (
          type === 'athlete' ||
          type === 'positive_athlete' ||
          type === 'college_athlete' ||
          type === 'professional' ||
          type === 'team_student'
        );
      },
      isCoach: () => {
        const type = get().profileType;
        return type === 'coach' || type === 'positive_coach' || type === 'team_coach';
      },
      isParent: () => get().profileType === 'parent',
      isSponsor: () => get().profileType === 'sponsor',
      isAdmin: () => get().profileType === 'admin',
      isAthleticsDirector: () => get().profileType === 'athletics_director',
    }),
    {
      name: 'positive-athlete-auth-storage',
    }
  )
);

// Helper to check hydration status
export const hasHydrated = () => useAuthStore.persist?.hasHydrated();

// Initialize hydration
export const initializeAuthStore = () => {
  const state = useAuthStore.getState();
  if (state.isAuthenticated && !state.user) {
    // If we have an inconsistent state, clear it
    state.logout();
  }
};
