/**
 * System Alert types for frontend integration
 * Matches the backend API response structure from SystemAlertData.php
 */

import type { Notification } from '@/components/shared/NotificationCarousel';

export interface MobileActionData {
  type: 'external_url' | 'internal_route';
  url?: string;
  web_route?: string;
  mobile_screen?: string;
  mobile_params?: Record<string, any>;
}

export interface SystemAlert {
  id: number;
  title: string;
  message: string;
  action_url?: string;
  action_label?: string;
  action_type: 'external_url' | 'internal_route' | null;
  web_action_url?: string;
  mobile_action_data?: MobileActionData;
  created_at: string;
}

/**
 * Transform SystemAlert to Notification interface for NotificationCarousel
 */
export function transformSystemAlertToNotification(alert: SystemAlert): Notification {
  return {
    id: alert.id.toString(),
    title: alert.title,
    message: alert.message,
    buttonUrl: alert.web_action_url || alert.action_url,
    buttonLabel: alert.action_label,
    variant: 'default' as const,
    icon: undefined, // Can be added later if needed
  };
}

/**
 * System Alert interaction types
 */
export type SystemAlertInteractionType = 'viewed' | 'clicked' | 'dismissed';

/**
 * API response wrapper for system alerts
 */
export interface SystemAlertsResponse {
  data: SystemAlert[];
}

/**
 * API response for interaction tracking
 */
export interface SystemAlertInteractionResponse {
  success: boolean;
  message?: string;
}
