'use client';

import { useEffect, useState, type ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import RouteGuard from '@/components/guards/RouteGuard';
import LoadingScreen from '@/components/loading/LoadingScreen';
import AuthProvider from '@/components/providers/AuthProvider';
import { Modal } from '@/components/shared/interactive/Modal';
import FeedbackButton from '@/components/ui/FeedbackButton';
import ToastNotify from '@/components/ui/ToastNotify';
import { hasHydrated } from '@/stores/auth.store';

const queryClient = new QueryClient();

export default function ClientLayout({ children }: { children: ReactNode }) {
  const [isClientReady, setIsClientReady] = useState(false);
  const [isFeedbackVisible, setIsFeedbackVisible] = useState(true);
  const isHydrated = hasHydrated();

  useEffect(() => {
    setIsClientReady(true);
  }, []);

  const handleRemoveFeedback = () => {
    setIsFeedbackVisible(false);
  };

  if (!isClientReady || !isHydrated) {
    return <LoadingScreen />;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <RouteGuard>
          {children}
          <Modal />
          <ToastNotify />
          <FeedbackButton
            isFeedbackVisible={isFeedbackVisible}
            handleRemoveFeedback={handleRemoveFeedback}
          />
        </RouteGuard>
      </AuthProvider>
    </QueryClientProvider>
  );
}
