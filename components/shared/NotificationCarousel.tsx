'use client';

import React, { useEffect, useState, useRef } from 'react';
import { faChevronCircleLeft, faChevronCircleRight } from '@fortawesome/pro-light-svg-icons';
import { faArrowRight } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import type { Swiper as SwiperType } from 'swiper';
import { Navigation, Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import { cn } from '@/lib/utils';
// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
// Import custom styles to override Swiper's default pagination
import './NotificationCarousel.css';

export interface Notification {
  id: string;
  icon?: React.ReactNode;
  title?: string;
  message?: string;
  buttonUrl?: string;
  buttonLabel?: string;
  variant?: 'default' | 'success' | 'warning' | 'error';
}

interface NotificationCarouselProps {
  notifications: Notification[];
  className?: string;
  onNotificationView?: (id: string) => void;
  onNotificationClick?: (id: string) => void;
}

interface NavigationButtonProps {
  direction: 'prev' | 'next';
  onClick: () => void;
  disabled: boolean;
}

const variantStyles = {
  default: {
    border: 'border-[#002B5C]',
    text: 'text-[#002B5C]',
    bg: 'bg-[#002B5C]',
    icon: 'text-[#002B5C] bg-blue-100',
    inactive: 'bg-[#d7dcde]',
  },
  success: {
    border: 'border-green-600',
    text: 'text-green-600',
    bg: 'bg-green-600',
    icon: 'text-green-600 bg-green-100',
    inactive: 'bg-[#d7dcde]',
  },
  warning: {
    border: 'border-yellow-600',
    text: 'text-yellow-600',
    bg: 'bg-yellow-600',
    icon: 'text-yellow-600 bg-yellow-100',
    inactive: 'bg-[#d7dcde]',
  },
  error: {
    border: 'border-[#d50032]',
    text: 'text-[#d50032]',
    bg: 'bg-[#d50032]',
    icon: 'text-[#d50032] bg-red-100',
    inactive: 'bg-[#d7dcde]',
  },
};

const NavigationButton: React.FC<NavigationButtonProps> = ({ direction, onClick, disabled }) => {
  const isNext = direction === 'next';
  const icon = isNext ? faChevronCircleRight : faChevronCircleLeft;

  return (
    <button
      type="button"
      onClick={onClick}
      className={cn(
        'rounded-full bg-surface-secondary flex items-center justify-center',
        disabled ? 'text-gray-200 cursor-not-allowed' : 'text-brand-red hover:text-red-700',
        'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
      )}
      disabled={disabled}
      aria-label={isNext ? 'Next notification' : 'Previous notification'}
    >
      <FontAwesomeIcon icon={icon} className="!size-6 text-current" />
    </button>
  );
};

export default function NotificationCarousel({
  notifications,
  className = '',
  onNotificationView,
  onNotificationClick,
}: NotificationCarouselProps) {
  const [swiper, setSwiper] = useState<SwiperType | null>(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const initialViewTracked = useRef(false);
  const viewedSlides = useRef(new Set<string>());

  useEffect(() => {
    if (swiper) {
      const updateState = () => {
        const newActiveIndex = swiper.activeIndex;
        setActiveIndex(newActiveIndex);
        setIsBeginning(swiper.isBeginning);
        setIsEnd(swiper.isEnd);

        // Track view when slide changes (only once per slide)
        if (onNotificationView && notifications[newActiveIndex]) {
          const slideId = notifications[newActiveIndex].id;
          if (!viewedSlides.current.has(slideId)) {
            onNotificationView(slideId);
            viewedSlides.current.add(slideId);
          }
        }
      };

      swiper.on('slideChange', updateState);
      swiper.on('snapGridLengthChange', updateState);

      return () => {
        swiper.off('slideChange', updateState);
        swiper.off('snapGridLengthChange', updateState);
      };
    }
  }, [swiper, onNotificationView, notifications]);

  // Track initial view when component mounts (only once)
  useEffect(() => {
    if (onNotificationView && notifications.length > 0 && notifications[0] && !initialViewTracked.current) {
      const slideId = notifications[0].id;
      onNotificationView(slideId);
      viewedSlides.current.add(slideId);
      initialViewTracked.current = true;
    }
  }, [onNotificationView, notifications]);

  if (!notifications?.length) return null;

  return (
    <div className={cn('relative w-full', className)}>
      <div className="relative">
        {/* Navigation Buttons */}
        <div className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 z-10">
          <NavigationButton
            direction="prev"
            onClick={() => swiper?.slidePrev()}
            disabled={isBeginning}
          />
        </div>

        <div className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 z-10">
          <NavigationButton direction="next" onClick={() => swiper?.slideNext()} disabled={isEnd} />
        </div>

        <Swiper
          onSwiper={setSwiper}
          modules={[Navigation, Pagination]}
          slidesPerView={1}
          spaceBetween={24}
          pagination={false}
          className="w-full"
          autoHeight
        >
          {notifications.map(notification => {
            const variant = notification.variant || 'default';
            const variantStyle = variantStyles[variant];

            return (
              <SwiperSlide key={notification.id}>
                <div className="bg-white rounded-4xl shadow px-10 py-8 border border-brand-red">
                  <div className="flex items-start justify-between flex-wrap gap-4 lg:flex-nowrap">
                    <div className="flex gap-x-4">
                      {notification.icon && (
                        <div className="size-6 flex items-center justify-center text-brand-red shrink-0">
                          {notification.icon}
                        </div>
                      )}
                      <div>
                        {notification.title && (
                          <h3 className="font-bold text-base mb-2">{notification.title}</h3>
                        )}
                        {notification.message && (
                          <p className="text-text-secondary text-sm line-clamp-3">
                            {notification.message}
                          </p>
                        )}
                      </div>
                    </div>

                    {notification.buttonUrl && (
                      <button
                        onClick={() => onNotificationClick?.(notification.id)}
                        className={`${variantStyle.text} pl-10 whitespace-nowrap hover:opacity-80 text-sm font-medium inline-flex items-center lg:pl-0 transition-opacity`}
                      >
                        {notification.buttonLabel || 'Learn More'}
                        <FontAwesomeIcon icon={faArrowRight} className="ml-2 size-4" />
                      </button>
                    )}
                  </div>
                </div>
              </SwiperSlide>
            );
          })}
        </Swiper>
      </div>

      {/* Custom pagination with fraction and dots */}
      <div className="flex gap-4 justify-center items-center my-6">
        {/* Fraction pagination */}
        <div className="bg-brand-red text-white text-sm/none font-bold px-3 py-1 rounded-full">
          {`${activeIndex + 1}/${notifications.length}`}
        </div>

        {/* Dot pagination */}
        <div className="flex items-center gap-2">
          {notifications.map((_, index) => (
            <button
              key={index}
              onClick={() => swiper?.slideTo(index)}
              className={clsx(
                'size-4 border-2 border-white rounded-full transition-colors shrink-0',
                index === activeIndex ? 'bg-brand-red' : 'bg-grey-3'
              )}
              aria-label={`Go to notification ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
