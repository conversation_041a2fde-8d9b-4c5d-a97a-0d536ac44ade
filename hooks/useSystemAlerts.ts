import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useMemo, useCallback } from 'react';
import { dashboardService } from '@/services/dashboard.service';
import { useToastNotify } from '@/stores/toastNotify.store';
import type { SystemAlert, SystemAlertInteractionType } from '@/types/systemAlert';
import { transformSystemAlertToNotification } from '@/types/systemAlert';
import type { Notification } from '@/components/shared/NotificationCarousel';

/**
 * Hook for managing system alerts state and interactions
 * @param context - The context where alerts are being displayed ('dashboard', 'profile', or 'my-school')
 */
export function useSystemAlerts(context: 'dashboard' | 'profile' | 'my-school' = 'dashboard') {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { toastNotify } = useToastNotify();

  // Query to fetch system alerts
  const {
    data: systemAlerts = [],
    isLoading: isLoadingAlerts,
    error: alertsError,
    refetch: refetchAlerts,
  } = useQuery<SystemAlert[], Error>({
    queryKey: ['dashboard', 'system-alerts'],
    queryFn: () => dashboardService.getSystemAlerts(),
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Transform system alerts to notification format (memoized to prevent infinite loops)
  const notifications: Notification[] = useMemo(
    () => systemAlerts.map(transformSystemAlertToNotification),
    [systemAlerts]
  );

  // Mutation for tracking alert views
  const viewAlertMutation = useMutation({
    mutationFn: (alertId: number) => dashboardService.markAlertAsViewed(alertId),
    onError: (error) => {
      console.error('Error tracking alert view:', error);
      // Don't show toast for view tracking errors as they're not critical
    },
  });

  // Mutation for tracking alert clicks
  const clickAlertMutation = useMutation({
    mutationFn: (alertId: number) => dashboardService.markAlertAsClicked(alertId),
    onSuccess: () => {
      // Refetch alerts to remove dismissed ones
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'system-alerts'] });
    },
    onError: (error) => {
      console.error('Error tracking alert click:', error);
      toastNotify('Failed to track alert interaction', 'error');
    },
  });

  // Mutation for dismissing alerts
  const dismissAlertMutation = useMutation({
    mutationFn: (alertId: number) => dashboardService.dismissAlert(alertId),
    onSuccess: () => {
      // Refetch alerts to remove dismissed ones
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'system-alerts'] });
    },
    onError: (error) => {
      console.error('Error dismissing alert:', error);
      toastNotify('Failed to dismiss alert', 'error');
    },
  });

  // Handle alert view tracking (memoized to prevent infinite loops)
  const handleAlertView = useCallback((notificationId: string) => {
    const alertId = parseInt(notificationId, 10);
    if (!isNaN(alertId)) {
      viewAlertMutation.mutate(alertId);
    }
  }, [viewAlertMutation]);

  // Handle alert click and navigation (memoized to prevent infinite loops)
  const handleAlertClick = useCallback((notificationId: string) => {
    const alertId = parseInt(notificationId, 10);
    const alert = systemAlerts.find(a => a.id === alertId);
    
    if (!alert) {
      console.error('Alert not found for click handling:', alertId);
      return;
    }

    // Track the click (this will also dismiss the alert)
    clickAlertMutation.mutate(alertId);

    // Handle navigation based on action type
    if (alert.action_type === 'external_url') {
      // Open external URL in new tab
      const url = alert.web_action_url || alert.action_url;
      if (url) {
        window.open(url, '_blank', 'noopener,noreferrer');
      }
    } else if (alert.action_type === 'internal_route') {
      // Use Next.js router for internal navigation
      const url = alert.web_action_url || alert.action_url;
      if (url) {
        router.push(url);
      }
    } else {
      // Legacy alert - treat as external URL for backward compatibility
      const url = alert.web_action_url || alert.action_url;
      if (url) {
        window.open(url, '_blank', 'noopener,noreferrer');
      }
    }
  }, [clickAlertMutation, systemAlerts, router]);

  // Handle manual alert dismissal (if needed in the future)
  const handleAlertDismiss = (notificationId: string) => {
    const alertId = parseInt(notificationId, 10);
    if (!isNaN(alertId)) {
      dismissAlertMutation.mutate(alertId);
    }
  };

  // Show error toast if alerts failed to load
  if (alertsError) {
    console.error('Error loading system alerts:', alertsError);
    // Only show toast once per error to avoid spam
    if (!alertsError.message.includes('already shown')) {
      toastNotify('Failed to load system alerts', 'error');
      // Mark error as shown to prevent repeated toasts
      alertsError.message += ' (already shown)';
    }
  }

  return {
    // Data
    systemAlerts,
    notifications,
    
    // Loading states
    isLoadingAlerts,
    isTrackingView: viewAlertMutation.isPending,
    isTrackingClick: clickAlertMutation.isPending,
    isDismissing: dismissAlertMutation.isPending,
    
    // Error states
    alertsError,
    
    // Actions
    handleAlertView,
    handleAlertClick,
    handleAlertDismiss,
    refetchAlerts,
  };
}
